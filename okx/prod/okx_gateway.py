import base64
import hashlib
import hmac
import json
import sys
import time
import traceback
from copy import copy
from datetime import datetime, timedelta
from urllib.parse import urlencode
from types import TracebackType
from collections.abc import Callable
from collections import defaultdict
from threading import Lock, Thread
from queue import Queue, Empty

from numpy import format_float_positional

# Global dict for gateway extra info: {symbol: {"leverage": {gateway_name: val}, "max_notional_value": {gateway_name: val}}}
symbol_gateway_extra = defaultdict(lambda: {"leverage": {}, "max_notional_value": {}})
from vnpy_evo.event import EventEngine, EVENT_TIMER, Event
from vnpy_evo.trader.constant import (
    Direction,
    Exchange,
    Interval,
    Offset,
    OrderType,
    Product,
    Status
)
from vnpy_evo.trader.gateway import BaseGateway
from vnpy_evo.trader.utility import round_to, ZoneInfo
from vnpy_evo.trader.object import (
    AccountData,
    BarData,
    CancelRequest,
    ContractData,
    HistoryRequest,
    OrderData,
    OrderRequest,
    PositionData,
    SubscribeRequest,
    TickData,
    TradeData
)
from .rest_client import Request, Response, RestClient
from .websocket_client2 import WebsocketClient


# UTC timezone
CHINA_TZ: ZoneInfo = ZoneInfo("Asia/Shanghai")

# Real server hosts
REAL_REST_HOST: str = "https://www.okx.com"
REAL_PUBLIC_HOST: str = "wss://ws.okx.com:8443/ws/v5/public"
REAL_PRIVATE_HOST: str = "wss://ws.okx.com:8443/ws/v5/private"
REAL_BUSINESS_HOST: str = "wss://ws.okx.com:8443/ws/v5/business"

# AWS server hosts
AWS_REST_HOST: str = "https://aws.okx.com"
AWS_PUBLIC_HOST: str = "wss://wsaws.okx.com:8443/ws/v5/public"
AWS_PRIVATE_HOST: str = "wss://wsaws.okx.com:8443/ws/v5/private"
AWS_BUSINESS_HOST: str = "wss://wsaws.okx.com:8443/ws/v5/business"

# Demo server hosts
DEMO_REST_HOST: str = "https://www.okx.com"
DEMO_PUBLIC_HOST: str = "wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999"
DEMO_PRIVATE_HOST: str = "wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999"
DEMO_BUSINESS_HOST: str = "wss://wspap.okx.com:8443/ws/v5/business?brokerId=9999"

# Order status map
STATUS_OKX2VT: dict[str, Status] = {
    "live": Status.NOTTRADED,
    "partially_filled": Status.PARTTRADED,
    "filled": Status.ALLTRADED,
    "canceled": Status.CANCELLED,
    "mmp_canceled": Status.CANCELLED
}

# Order type map
ORDERTYPE_OKX2VT: dict[str, OrderType] = {
    "limit": OrderType.LIMIT,
    "fok": OrderType.FOK,
    "ioc": OrderType.FAK
}
ORDERTYPE_VT2OKX: dict[OrderType, str] = {v: k for k, v in ORDERTYPE_OKX2VT.items()}

# Direction map
DIRECTION_OKX2VT: dict[str, Direction] = {
    "buy": Direction.LONG,
    "sell": Direction.SHORT
}
DIRECTION_VT2OKX: dict[Direction, str] = {v: k for k, v in DIRECTION_OKX2VT.items()}

# Kline interval map
INTERVAL_VT2OKX: dict[Interval, str] = {
    Interval.MINUTE: "1m",
    Interval.HOUR: "1H",
    Interval.DAILY: "1D",
}

# Product type map
PRODUCT_OKX2VT: dict[str, Product] = {
    "SWAP": Product.SWAP,
    # "SPOT": Product.SPOT,
    # "FUTURES": Product.FUTURES
}
PRODUCT_VT2OKX: dict[Product, str] = {v: k for k, v in PRODUCT_OKX2VT.items()}


class OkxGateway(BaseGateway):
    """
    The OKX trading gateway for VeighNa.

    Only support net mode
    """

    default_name = "OKX"

    default_setting: dict = {
        "API Key": "",
        "Secret Key": "",
        "Passphrase": "",
        "Server": ["REAL", "AWS", "DEMO"],
        "Proxy Host": "",
        "Proxy Port": 0,
    }

    exchanges: Exchange = [Exchange.GLOBAL]

    def __init__(self, event_engine: EventEngine, gateway_name: str) -> None:
        """
        The init method of the gateway.

        event_engine: the global event engine object of VeighNa
        gateway_name: the unique name for identifying the gateway
        """
        super().__init__(event_engine, gateway_name)

        self.key: str = ""
        self.secret: str = ""
        self.passphrase: str = ""
        self.server: str = ""
        self.proxy_host: str = ""
        self.proxy_port: int = 0

        self.get_server_time_interval: int = 0
        self.server_time_check_interval: int = 300 // event_engine._interval
        
        # 添加合约查询计数器
        self.get_contract_interval: int = 0
        self.contract_check_interval: int = 3600 // event_engine._interval  # 每小时查询一次

        self.orders: dict[str, OrderData] = {}
        self.order_lock: Lock = Lock()  # 添加线程锁
        self.local_orderids: set[str] = set()

        self.symbol_contract_map: dict[str, ContractData] = {}
        self.name_contract_map: dict[str, ContractData] = {}

        self.rest_api: RestApi = RestApi(self)
        self.public_api: PublicApi = PublicApi(self)
        self.private_api: PrivateApi = PrivateApi(self)

    def connect(self, setting: dict) -> None:
        """
        Start server connections.

        This method establishes connections to OKX servers
        using the provided settings.

        Parameters:
            setting: A dictionary containing connection parameters including
                    API credentials, server selection, and proxy configuration
        """
        self.key = setting["API Key"]
        self.secret = setting["Secret Key"]
        self.passphrase = setting["Passphrase"]
        self.server = setting["Server"]
        self.proxy_host = setting["Proxy Host"]
        self.proxy_port = setting["Proxy Port"]

        self.rest_api.connect(
            self.key,
            self.secret,
            self.passphrase,
            self.server,
            self.proxy_host,
            self.proxy_port
        )

    def connect_ws_api(self) -> None:
        """
        Connect to OKX websocket API.
        """
        self.public_api.connect(
            self.server,
            self.proxy_host,
            self.proxy_port,
        )
        self.private_api.connect(
            self.key,
            self.secret,
            self.passphrase,
            self.server,
            self.proxy_host,
            self.proxy_port,
        )

        self.event_engine.unregister(EVENT_TIMER, self.process_timer_event)
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def process_timer_event(self, event: Event) -> None:
        """process the server time update and contract query."""
        self.get_server_time_interval += 1
        self.get_contract_interval += 1
        
        if self.get_server_time_interval >= self.server_time_check_interval:
            self.rest_api.query_time()
            self.get_server_time_interval = 0
            
        # 每小时查询一次合约信息
        if self.get_contract_interval >= self.contract_check_interval:
            self.rest_api.query_contract()
            self.get_contract_interval = 0

    def subscribe(self, req: SubscribeRequest) -> None:
        """
        Subscribe to market data.

        Parameters:
            req: Subscription request object containing symbol information
        """
        self.public_api.subscribe(req)

    def unsubscribe(self, req: SubscribeRequest) -> None:
        """
        Unsubscribe from market data.

        Parameters:
            req: Subscription request object containing symbol information
        """
        self.public_api.unsubscribe(req)

    def send_order(self, req: OrderRequest) -> str:
        """
        Send new order to OKX.

        This function delegates order placement to the private websocket API,
        which handles validation, order generation, and submission to the exchange.

        Parameters:
            req: Order request object containing order details

        Returns:
            str: The VeighNa order ID if successful, empty string otherwise
        """
        return self.private_api.send_order(req)

    def cancel_order(self, req: CancelRequest) -> None:
        """
        Cancel existing order on OKX.

        This function delegates order cancellation to the private websocket API,
        which determines the appropriate ID type to use and submits the request.

        Parameters:
            req: Cancel request object containing order details
        """
        self.private_api.cancel_order(req)

    def query_account(self) -> None:
        """
        Not required since OKX provides websocket update for account balances.
        """
        pass

    def query_position(self) -> None:
        """
        Query holding positions.
        """
        self.rest_api.query_position()

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        """
        Query historical kline data.

        Parameters:
            req: History request object containing query parameters

        Returns:
            list[BarData]: List of historical kline data bars
        """
        return self.rest_api.query_history(req)

    def cancel_all(self) -> None:
        """
        Cancel all open orders.

        This method cancels all active orders by querying current open orders
        and then batch cancelling them using OKX's batch-cancel-orders API.
        """
        self.private_api.cancel_all_orders()

    def set_leverage(self, symbol: str, leverage: int, mgn_mode: str = "cross") -> None:
        """
        设置合约杠杆倍数

        Parameters:
            symbol: 交易对符号 (如: BTC-USDT-SWAP)
            leverage: 杠杆倍数
            mgn_mode: 保证金模式 (isolated: 逐仓, cross: 全仓)
        """
        self.rest_api.set_leverage(symbol, leverage, mgn_mode)

    def query_leverage(self, symbol: str) -> None:
        """
        查询合约杠杆倍数

        Parameters:
            symbol: 交易对符号 (如: BTC-USDT-SWAP)
        """
        self.rest_api.query_symbol_config(symbol)

    def query_symbol_config(self, symbol: str = None) -> None:
        """
        查询合约的杠杆配置

        Parameters:
            symbol: 交易对符号，如果为 None 则查询所有合约，否则只查询指定合约
        """
        self.rest_api.query_symbol_config(symbol)

    def close(self) -> None:
        """
        Close server connections.

        This method stops all API connections and releases resources.
        """
        self.rest_api.stop()
        self.public_api.stop()
        self.private_api.stop()

    def on_order(self, order: OrderData) -> None:
        """
        Save a copy of order and then push to event engine.

        Parameters:
            order: Order data object
        """
        self.orders[order.orderid] = order
        super().on_order(order)

    def get_order(self, orderid: str) -> OrderData:
        """
        Get previously saved order by order id.

        Parameters:
            orderid: The ID of the order to retrieve

        Returns:
            OrderData: Order data object if found, None otherwise
        """
        return self.orders.get(orderid, None)

    def on_contract(self, contract: ContractData) -> None:
        """
        Save a copy of contract and then push to event engine.

        Parameters:
            contract: Contract data object
        """
        # 保留原有 extra 字段
        if contract.extra is None:
            contract.extra = {}

        # 添加全局存储的extra信息到合约对象
        contract.extra.update(symbol_gateway_extra[contract.symbol])

        self.symbol_contract_map[contract.symbol] = contract
        self.name_contract_map[contract.name] = contract

        # if contract.symbol.lower().startswith('btcusdt') or contract.symbol.lower().startswith('ethusdt'):
        #     self.write_log(contract.vt_symbol)
        super().on_contract(contract)

    def get_contract_by_symbol(self, symbol: str) -> ContractData | None:
        """
        Get contract by VeighNa symbol.

        Parameters:
            symbol: The symbol of the contract

        Returns:
            ContractData: Contract data object if found, None otherwise
        """
        return self.symbol_contract_map.get(symbol, None)

    def get_contract_by_name(self, name: str) -> ContractData | None:
        """
        Get contract by exchange symbol name.

        Parameters:
            name: The name of the contract

        Returns:
            ContractData: Contract data object if found, None otherwise
        """
        return self.name_contract_map.get(name, None)

    def parse_order_data(self, data: dict, gateway_name: str) -> OrderData:
        """
        Parse dict to order data.

        This function converts OKX order data into a VeighNa OrderData object.
        It extracts and maps all relevant fields from the exchange response.

        Parameters:
            data: Order data from OKX
            gateway_name: Gateway name for identification

        Returns:
            OrderData: VeighNa order object
        """
        contract: ContractData = self.get_contract_by_name(data["instId"])

        order_id: str = data["clOrdId"]
        if order_id:
            self.local_orderids.add(order_id)
        else:
            order_id = data["ordId"]

        # Convert OKX order volumes from contracts to coins for Binance compatibility
        order_volume_coins = float(format_float(float(data["sz"]) * contract.size))
        traded_volume_coins = float(format_float(float(data["accFillSz"]) * contract.size))

        order: OrderData = OrderData(
            symbol=contract.symbol,
            exchange=Exchange.GLOBAL,
            type=ORDERTYPE_OKX2VT[data["ordType"]],
            orderid=order_id,
            direction=DIRECTION_OKX2VT[data["side"]],
            offset=Offset.NONE,
            traded=traded_volume_coins,
            price=float(data["px"]),
            volume=order_volume_coins,
            datetime=parse_timestamp(data["cTime"]),
            status=STATUS_OKX2VT[data["state"]],
            gateway_name=gateway_name,
        )
        return order

    def write_log(self, msg: str) -> None:
        """
        Write a log event from gateway with caller information.
        """
        if msg.startswith("["):
            super().write_log(msg)
        else:
            frame = sys._getframe(1)
            func_name = frame.f_code.co_name
            line_no = frame.f_lineno
            class_name = self.__class__.__name__
            formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
            super().write_log(formatted_msg)


class RestApi(RestClient):
    """The REST API of OkxGateway"""

    def __init__(self, gateway: OkxGateway) -> None:
        """
        The init method of the api.

        Parameters:
            gateway: the parent gateway object for pushing callback data.
        """
        super().__init__()

        self.gateway: OkxGateway = gateway
        self.gateway_name: str = gateway.gateway_name

        self.key: str = ""
        self.secret: bytes = b""
        self.passphrase: str = ""
        self.simulated: bool = False

        self.time_offset_ms: float = 0
        self.product_ready: set = set()

    def sign(self, request: Request) -> Request:
        """
        Standard callback for signing a request.

        This method adds the necessary authentication parameters and signature
        to requests that require API key authentication.

        Parameters:
            request: Request object to be signed

        Returns:
            Request: Modified request with authentication parameters
        """
        # Public API does not need to sign
        if "public" in request.path:
            return request

        # Generate signature
        now: datetime = datetime.utcnow()
        now = now - timedelta(milliseconds=self.time_offset_ms)
        timestamp: str = now.isoformat("T", "milliseconds") + "Z"

        if request.data:
            request.data = json.dumps(request.data)
        else:
            request.data = ""

        if request.params:
            path: str = request.path + "?" + urlencode(request.params)
        else:
            path = request.path

        msg: str = timestamp + request.method + path + request.data
        signature: bytes = generate_signature(msg, self.secret)

        # Add request header
        request.headers = {
            "OK-ACCESS-KEY": self.key,
            "OK-ACCESS-SIGN": signature.decode(),
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": self.passphrase,
            "Content-Type": "application/json"
        }

        if self.simulated:
            request.headers["x-simulated-trading"] = "1"

        return request

    def connect(
        self,
        key: str,
        secret: str,
        passphrase: str,
        server: str,
        proxy_host: str,
        proxy_port: int,
    ) -> None:
        """
        Start server connection.

        This method establishes a connection to OKX REST API server
        using the provided credentials and configuration.

        Parameters:
            key: API Key for authentication
            secret: API Secret for request signing
            passphrase: API Passphrase for authentication
            server: Server type ("REAL", "AWS", or "DEMO")
            proxy_host: Proxy server hostname or IP
            proxy_port: Proxy server port
        """
        self.key = key
        self.secret = secret.encode()
        self.passphrase = passphrase

        if server == "DEMO":
            self.simulated = True

        self.connect_time = int(datetime.now().strftime("%y%m%d%H%M%S"))

        server_hosts: dict[str, str] = {
            "REAL": REAL_REST_HOST,
            "AWS": AWS_REST_HOST,
            "DEMO": DEMO_REST_HOST,
        }

        host: str = server_hosts[server]
        self.init(host, proxy_host, proxy_port)

        self.start()
        self.gateway.write_log("REST API started")

        self.query_time()
        self.query_contract()
        self.set_position_mode()

    def set_position_mode(self) -> None:
        self.add_request(
            "POST",
            "/api/v5/account/set-position-mode",
            data={"posMode": "net_mode"},
            callback=self.on_position_mode)

    def set_leverage(self, symbol: str, leverage: int, mgn_mode: str = "cross") -> None:
        """
        设置合约杠杆倍数

        Parameters:
            symbol: 交易对符号 (如: BTC-USDT-SWAP)
            leverage: 杠杆倍数
            mgn_mode: 保证金模式 (isolated: 逐仓, cross: 全仓)
        """
        # 获取合约信息
        contract: ContractData | None = self.gateway.get_contract_by_symbol(symbol)
        if not contract:
            self.gateway.write_log(f"设置杠杆倍数失败，找不到合约：{symbol}")
            return

        # 只对SWAP和FUTURES设置杠杆
        if contract.product not in [Product.SWAP, Product.FUTURES]:
            self.gateway.write_log(f"设置杠杆倍数失败，该合约类型不支持杠杆：{symbol}")
            return

        # 构建请求数据
        data = {
            "instId": contract.name,
            "lever": str(leverage),
            "mgnMode": mgn_mode
        }

        self.add_request(
            "POST",
            "/api/v5/account/set-leverage",
            data=data,
            callback=self.on_set_leverage
        )

    def on_set_leverage(self, packet: dict, request: Request) -> None:
        """
        设置杠杆倍数的回调函数

        Parameters:
            packet: 服务器响应数据
            request: 原始请求对象
        """
        if packet.get("code") == "0":
            data = packet.get("data", [])
            if data:
                result = data[0]
                lever = result.get("lever", "")
                mgn_mode = result.get("mgnMode", "")
                inst_id = result.get("instId", "")
                pos_side = result.get("posSide", "")

                # 更新 symbol_gateway_extra
                if inst_id:
                    contract: ContractData | None = self.gateway.get_contract_by_name(inst_id)
                    if contract:
                        # 存储杠杆信息到全局字典
                        symbol_gateway_extra[contract.symbol]["leverage"][self.gateway_name] = int(lever)

                        # 更新合约的extra信息
                        self.gateway.on_contract(contract)

                # 构建详细的成功日志
                log_msg = f"设置杠杆倍数成功 - 杠杆倍数: {lever}x, 保证金模式: {'全仓' if mgn_mode == 'cross' else '逐仓'}"
                if inst_id:
                    log_msg += f", 产品ID: {inst_id}"
                if pos_side:
                    log_msg += f", 持仓方向: {pos_side}"

                self.gateway.write_log(log_msg)
            else:
                self.gateway.write_log("设置杠杆倍数成功")
        else:
            error_msg = packet.get("msg", "未知错误")
            error_code = packet.get("code", "")
            self.gateway.write_log(f"设置杠杆倍数失败 - 错误码: {error_code}, 错误信息: {error_msg}")



    def query_symbol_config(self, symbol: str = None) -> None:
        """
        查询合约的杠杆配置

        OKX 的杠杆查询 API 支持批量查询，但需要分批处理以避免超过 API 限制
        参考文档：GET /api/v5/account/leverage-info

        Parameters:
            symbol: 交易对符号，如果为 None 则查询所有合约，否则只查询指定合约
        """
        # 获取所有 SWAP 合约（永续合约）
        swap_contracts = [contract for contract in self.gateway.symbol_contract_map.values()
                         if contract.name.endswith("USDT-SWAP")]

        # 根据 symbol 参数筛选需要查询的合约
        if symbol:
            # 查询单个合约：从 swap_contracts 中筛选出指定的合约
            target_contracts = [contract for contract in swap_contracts if contract.symbol == symbol]
            if not target_contracts:
                self.gateway.write_log(f"查询杠杆配置失败，找不到合约：{symbol}")
                return
            log_prefix = f"single contract: {symbol}"
        else:
            # 查询所有合约
            target_contracts = swap_contracts
            if not target_contracts:
                self.gateway.write_log("No SWAP contracts found for leverage query")
                return
            log_prefix = f"{len(target_contracts)} contracts"

        # 分批查询，每批最多 20 个合约（OKX API 限制）
        batch_size = 5
        total_contracts = len(target_contracts)
        total_batches = (total_contracts + batch_size - 1) // batch_size

        for i in range(0, total_contracts, batch_size):
            batch_contracts = target_contracts[i:i + batch_size]
            inst_ids = [contract.name for contract in batch_contracts]

            # 构建请求参数
            params = {
                "instId": ",".join(inst_ids),  # 多个 instId 用逗号分隔（单个也可以用这种格式）
                "mgnMode": "cross",  # 固定为全仓模式
                "ccy":""
            }

            self.add_request(
                "GET",
                "/api/v5/account/leverage-info",
                params=params,
                callback=self.on_query_symbol_config
            )
            break

        batch_info = f" in {total_batches} batches" if total_batches > 1 else ""
        self.gateway.write_log(f"Started querying leverage config for {log_prefix}{batch_info}")

    def on_query_symbol_config(self, packet: dict, request: Request) -> None:
        """
        查询合约杠杆配置的回调函数（统一处理单个和批量查询）

        根据 OKX 文档，无论查询单个还是多个 instId，API 返回格式都相同，
        都是 data 数组，因此可以统一处理。

        Parameters:
            packet: 服务器响应数据
            request: 原始请求对象
        """
        # Check if query was successful
        if packet.get("code") != "0":
            msg = packet.get("msg", "Unknown error")
            self.gateway.write_log(f"Query leverage config failed: {msg}")
            return

        data = packet.get("data", [])
        if not data:
            self.gateway.write_log("Query leverage config completed: No leverage configurations found")
            return

        leverage_count = 0
        # 处理所有返回的杠杆配置（单个或多个都是相同的数组格式）
        for result in data:
            lever = result.get("lever", "")
            mgn_mode = result.get("mgnMode", "")
            inst_id = result.get("instId", "")

            # 获取对应的合约信息
            if inst_id:
                contract: ContractData | None = self.gateway.get_contract_by_name(inst_id)
                if contract:
                    # 存储杠杆信息到全局字典
                    symbol_gateway_extra[contract.symbol]["leverage"][self.gateway_name] = int(lever)

                    # 更新合约的extra信息
                    self.gateway.on_contract(contract)
                    leverage_count += 1

        # 输出统一的完成日志
        self.gateway.write_log(f"Query leverage config completed: {leverage_count} leverage configurations received.\n{symbol_gateway_extra}")

    def on_position_mode(self, packet: dict, request: Request):
        pass

    def query_time(self) -> None:
        """
        Query server time.

        This function sends a request to get the exchange server time,
        which is used to synchronize local time with server time.
        """
        self.add_request(
            "GET",
            "/api/v5/public/time",
            callback=self.on_query_time
        )

    def query_orders(self, for_cancel_all: bool = False) -> None:
        """
        Query open orders.

        This function sends a request to get all active orders
        that have not been fully filled or cancelled.
        
        Parameters:
            for_cancel_all: If True, orders will be batch cancelled after query
        """
        self.add_request(
            "GET",
            "/api/v5/trade/orders-pending",
            callback=self.on_query_orders,
            extra={"for_cancel_all": for_cancel_all}
        )

    def query_position(self) -> None:
        """
        Query holding positions.

        This function sends a request to get all holding positions
        for the account.
        """
        self.add_request(
            "GET",
            "/api/v5/account/positions",
            callback=self.on_query_position
        )

    def query_contract(self) -> None:
        """
        Query available contracts.

        This function sends a request to get exchange information,
        including all available trading instruments and their specifications.
        """
        for inst_type in PRODUCT_OKX2VT.keys():
            self.add_request(
                "GET",
                "/api/v5/public/instruments",
                callback=self.on_query_contract,
                params={"instType": inst_type}
            )

    def on_query_time(self, packet: dict, request: Request) -> None:
        """
        Callback of server time query.

        This function processes the server time response and calculates
        the time difference between local and server time for logging.

        Parameters:
            packet: Response data from the server
            request: Original request object
        """
        server_ts: float = float(packet["data"][0]["ts"])
        local_ts: float = float(time.time() * 1000)
        self.time_offset_ms: float = local_ts - server_ts

        server_dt: datetime = datetime.fromtimestamp(server_ts / 1000)
        local_dt: datetime = datetime.fromtimestamp(local_ts/1000)
        msg: str = f"server time: {server_dt}, local time: {local_dt}"
        self.gateway.write_log(msg)

    def on_query_orders(self, packet: dict, request: Request) -> None:
        """
        Callback of open orders query.

        This function processes the open orders response and
        creates OrderData objects for each active order.
        If for_cancel_all is True, it also triggers batch cancellation.

        Parameters:
            packet: Response data from the server
            request: Original request object
        """
        # Check if query was successful
        if packet.get("code") != "0":
            msg = packet.get("msg", "Unknown error")
            self.gateway.write_log(f"Query orders failed: {msg}")
            return
            
        for_cancel_all = request.extra.get("for_cancel_all", False) if request.extra else False
        order_data = packet.get("data", [])
        
        if not order_data:
            if for_cancel_all:
                self.gateway.write_log("No open orders found to cancel")
            else:
                self.gateway.write_log("No open orders found")
            return
        
        # Process orders and create OrderData objects
        for order_info in order_data:
            order: OrderData = self.gateway.parse_order_data(
                order_info,
                self.gateway_name
            )
            
            with self.gateway.order_lock:
                # Apply the same logic as in on_order method to avoid duplicate updates
                last_order: OrderData = self.gateway.get_order(order.orderid)
                
                # Always push first order update
                if not last_order:
                    self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                    self.gateway.on_order(order)
                    continue
                
                # If last_order is not active anymore, skip this update
                if not last_order.is_active():
                    # self.gateway.write_log(f"忽略订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                    continue
            
                # Calculate traded change with precision handling
                status_change: bool = order.status != last_order.status

                # Convert to contracts for precise calculation, then back to coins
                contract: ContractData = self.gateway.get_contract_by_symbol(order.symbol)
                if contract:
                    # Convert to contracts and calculate change
                    order_traded_contracts = order.traded / contract.size
                    last_traded_contracts = last_order.traded / contract.size
                    traded_change_contracts = round_to(order_traded_contracts - last_traded_contracts, contract.min_volume)
                    # Convert back to coins
                    traded_change = float(format_float(traded_change_contracts * contract.size))

                    # Log if precision handling made a difference
                    direct_change = order.traded - last_order.traded
                    if traded_change != direct_change:
                        self.gateway.write_log(f"warning {order.symbol} traded_change precision handled: {direct_change} -> {traded_change}")
                else:
                    # Fallback without precision handling
                    traded_change = order.traded - last_order.traded
                
                # Skip if no meaningful change
                if traded_change < 0:
                    continue
                if traded_change == 0 and not status_change:
                    continue
                
                # Push order update
                self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                self.gateway.on_order(order)
            
            # Push trade update on new trade (same logic as in on_order)
            if traded_change > 0:
                # Create trade object and push to gateway
                trade: TradeData = TradeData(
                    symbol=order.symbol,
                    exchange=order.exchange,
                    orderid=order.orderid,
                    tradeid=order_info.get("tradeId") or order.orderid,
                    direction=order.direction,
                    offset=order.offset,
                    price=float(order_info.get("fillPx") or order_info.get("avgPx") or order.price),
                    volume=traded_change,
                    datetime=parse_timestamp(order_info["uTime"]),
                    gateway_name=self.gateway_name,
                )
                self.gateway.write_log(f"收到成交回报，订单号：{trade.orderid}，成交号：{trade.tradeid}，价格：{trade.price}，数量：{trade.volume}")
                self.gateway.on_trade(trade)

        if for_cancel_all:
            # Prepare order list for batch cancellation
            order_list = []
            for order_info in order_data:
                # Create cancel args for each order
                cancel_args = {
                    "instId": order_info["instId"]
                }
                
                # Use clOrdId if available, otherwise use ordId
                if order_info.get("clOrdId"):
                    cancel_args["clOrdId"] = order_info["clOrdId"]
                else:
                    cancel_args["ordId"] = order_info["ordId"]
                
                order_list.append(cancel_args)
            
            # Send batch cancel request via WebSocket API
            if order_list:
                self.gateway.private_api.batch_cancel_orders(order_list)
                self.gateway.write_log(f"Queried {len(order_data)} orders, sending batch cancel requests for all orders")
            else:
                self.gateway.write_log("No orders found to cancel")
        else:
            self.gateway.write_log(f"Query orders completed: {len(order_data)} orders received")

    def on_query_position(self, packet: dict, request: Request) -> None:
        """
        Callback of holding positions query.

        This function processes the position data response and
        creates PositionData objects for each position.

        Parameters:
            packet: Response data from the server
            request: Original request object
        """
        # Check if query was successful
        if packet.get("code") != "0":
            msg = packet.get("msg", "Unknown error")
            self.gateway.write_log(f"Query positions failed: {msg}")
            return

        position_data = packet.get("data", [])
        
        if not position_data:
            self.gateway.write_log("No positions found")
            return

        # Process positions and create PositionData objects
        for pos_info in position_data:
            name: str = pos_info["instId"]
            contract: ContractData = self.gateway.get_contract_by_name(name)
            
            if not contract:
                self.gateway.write_log(f"Contract not found for position: {name}")
                continue

            # Convert OKX position volume from contracts to coins for Binance compatibility
            pos_contracts: float = float(pos_info.get("pos", "0"))
            pos_coins: float = float(format_float(pos_contracts * contract.size))
            price: float = get_float_value(pos_info, "avgPx")
            pnl: float = get_float_value(pos_info, "upl")

            position: PositionData = PositionData(
                symbol=contract.symbol,
                exchange=Exchange.GLOBAL,
                direction=Direction.NET,
                volume=pos_coins,
                price=price,
                pnl=pnl,
                gateway_name=self.gateway_name,
            )
            self.gateway.on_position(position)

        self.gateway.write_log(f"Query positions completed: {len(position_data)} positions received")

    def on_query_contract(self, packet: dict, request: Request) -> None:
        """
        Callback of available contracts query.

        This function processes the exchange info response and
        creates ContractData objects for each trading instrument.

        Parameters:
            packet: Response data from the server
            request: Original request object
        """
        data: list = packet["data"]

        for d in data:
            try:
                name: str = d["instId"]
                product: Product = PRODUCT_OKX2VT[d["instType"]]
                net_position: bool = True

                if product == Product.SPOT:
                    size: float = 1
                else:
                    # size = float(d["ctMult"])
                    size = float(d["ctVal"])

                match product:
                    case Product.SPOT:
                        symbol: str = name.replace("-", "") + "_SPOT_OKX"
                    case Product.SWAP:
                        base, quote, _ = name.split("-")
                        symbol = base + quote + "_SWAP_OKX"
                    case Product.FUTURES:
                        base, quote, expiry = name.split("-")
                        symbol = base + quote + "_" + expiry + "_OKX"

                pricetick: float = float(d["tickSz"])
                min_volume: float = float(d["minSz"])

                contract: ContractData = ContractData(
                    symbol=symbol,
                    exchange=Exchange.GLOBAL,
                    name=name,
                    product=product,
                    size=size,
                    pricetick=pricetick,
                    min_volume=min_volume,
                    history_data=True,
                    net_position=net_position,
                    gateway_name=self.gateway_name,
                )

                self.gateway.on_contract(contract)
            except Exception as e:
                self.gateway.write_log(f"Error processing contract data: {e}")

        self.gateway.write_log(f"{d['instType']} contract data received")

        # Connect to websocket API after all contract data received
        self.product_ready.add(contract.product)

        if len(self.product_ready) == len(PRODUCT_OKX2VT):
            self.query_orders()
            # self.query_symbol_config()  # 查询所有合约的杠杆配置

            self.gateway.connect_ws_api()

    def on_error(
        self,
        exc: type,
        value: Exception,
        tb: TracebackType,
        request: Request
    ) -> None:
        """
        General error callback.

        This function is called when an exception occurs in REST API requests.
        It logs the exception details for troubleshooting.

        Parameters:
            exc: Type of the exception
            value: Exception instance
            tb: Traceback object
            request: Original request object
        """
        detail: str = self.exception_detail(exc, value, tb, request)

        msg: str = f"Exception catched by REST API: {detail}"
        self.gateway.write_log(msg)

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        """
        Query kline history data.

        This function sends requests to get historical kline data
        for a specific trading instrument and time period.

        Parameters:
            req: History request object containing query parameters

        Returns:
            list[BarData]: List of historical kline data bars
        """
        # Validate symbol exists in contract map
        contract: ContractData | None = self.gateway.get_contract_by_symbol(req.symbol)
        if not contract:
            self.gateway.write_log(f"Query kline history failed, symbol not found: {req.symbol}")
            return []

        # Initialize buffer for storing bars
        buf: dict[datetime, BarData] = {}

        path: str = "/api/v5/market/history-candles"
        limit: str = "100"

        if not req.end:
            req.end = datetime.now()

        after: str = str(int(req.end.timestamp() * 1000))

        # Loop until no more data or request fails
        while True:
            # Create query params
            params: dict = {
                "instId": contract.name,
                "bar": INTERVAL_VT2OKX[req.interval],
                "limit": limit,
                "after": after
            }

            # Get response from server
            resp: Response = self.request(
                "GET",
                path,
                params=params
            )

            # Break loop if request is failed
            if resp.status_code // 100 != 2:
                log_msg: str = f"Query kline history failed, status code: {resp.status_code}, message: {resp.text}"
                self.gateway.write_log(log_msg)
                break
            else:
                data: dict = resp.json()
                bar_data: list = data.get("data", None)

                if not bar_data:
                    msg: str = data["msg"]
                    log_msg = f"No kline history data received, {msg}"
                    self.gateway.write_log(log_msg)
                    break

                for row in bar_data:
                    # OKX history-candles returns: [ts, o, h, l, c, vol, volCcy, volCcyQuote, confirm]
                    ts, op, hp, lp, cp, vol_contracts, vol_coins, vol_quote, _ = row

                    dt: datetime = parse_timestamp(ts)

                    # Use OKX volCcy for volume (coins like Binance) and volCcyQuote for turnover (USDT like Binance)
                    volume = float(vol_coins) if vol_coins else 0.0
                    turnover = float(vol_quote) if vol_quote else 0.0

                    bar: BarData = BarData(
                        symbol=req.symbol,
                        exchange=req.exchange,
                        datetime=dt,
                        interval=req.interval,
                        volume=volume,
                        turnover=turnover,
                        open_price=float(op),
                        high_price=float(hp),
                        low_price=float(lp),
                        close_price=float(cp),
                        gateway_name=self.gateway_name
                    )
                    buf[bar.datetime] = bar

                begin: str = bar_data[-1][0]
                begin_dt: datetime = parse_timestamp(begin)
                end: str = bar_data[0][0]
                end_dt: datetime = parse_timestamp(end)

                log_msg = f"Query kline history finished, {req.symbol} - {req.interval.value}, {begin_dt} - {end_dt}"
                self.gateway.write_log(log_msg)

                # Break if all bars have been queried
                if begin_dt <= req.start:
                    break

                # Update start time
                after = begin

        index: list[datetime] = list(buf.keys())
        index.sort()

        history: list[BarData] = [buf[i] for i in index]
        return history


class PublicApi(WebsocketClient):
    """The public websocket API of OkxGateway"""

    def __init__(self, gateway: OkxGateway) -> None:
        """
        The init method of the api.

        Parameters:
            gateway: the parent gateway object for pushing callback data.
        """
        super().__init__()

        self.gateway: OkxGateway = gateway
        self.gateway_name: str = gateway.gateway_name

        self.subscribed: dict[str, SubscribeRequest] = {}
        self.ticks: dict[str, TickData] = {}

        self.callbacks: dict[str, Callable] = {
            "tickers": self.on_ticker,
            "books5": self.on_depth
        }

        # OKX订阅流控参数
        # 限制: 多个频道总长度不能超过 64 KB
        # 限制: 订阅/取消订阅/登录 请求总次数限制为 480 次/小时，即7.5s一次
        self.max_packet_size: int = 60 * 1024  # 60KB，留4KB缓冲
        self.subscribe_interval: float = 7.5  # 7.5秒间隔
        
        # 订阅队列和线程
        self._subscribe_queue: Queue = Queue()
        self._unsubscribe_queue: Queue = Queue()
        self._subscribe_thread: Thread = None
        self._active_subscribe_thread: bool = False
        
        # 日志模式
        self.log_mode: bool = False
        self.last_ticker_time: dict[str, datetime] = {}  # 记录每个instId最后一次ticker的时间
        self.last_depth_time: dict[str, datetime] = {}  # 记录每个instId最后一次depth的时间
        self.ticker_count: dict[str, int] = {}  # 记录每个instId的ticker计数
        self.depth_count: dict[str, int] = {}  # 记录每个instId的depth计数
        self.start_time: dict[str, datetime] = {}  # 记录每个instId的开始时间

    def connect(
        self,
        server: str,
        proxy_host: str,
        proxy_port: int,
    ) -> None:
        """
        Start server connection.

        This method establishes a websocket connection to OKX public data stream.

        Parameters:
            server: Server type ("REAL", "AWS", or "DEMO")
            proxy_host: Proxy server hostname or IP
            proxy_port: Proxy server port
        """
        server_hosts: dict[str, str] = {
            "REAL": REAL_PUBLIC_HOST,
            "AWS": AWS_PUBLIC_HOST,
            "DEMO": DEMO_PUBLIC_HOST,
        }

        host: str = server_hosts[server]
        self.init(host, proxy_host, proxy_port, 10)

        self.start()
        self.start_subscribe_thread()

    def start_subscribe_thread(self) -> None:
        """启动订阅流控线程"""
        if self._subscribe_thread and self._subscribe_thread.is_alive():
            return

        self._active_subscribe_thread = True
        self._subscribe_thread = Thread(target=self._subscribe_process)
        self._subscribe_thread.daemon = True
        self._subscribe_thread.start()
        self.gateway.write_log("OKX订阅流控线程已启动")
        
    def enable_log_mode(self) -> None:
        """启用日志模式，用于跟踪ticker和depth推送频率"""
        self.log_mode = True
        self.gateway.write_log("日志模式已启用，将跟踪ticker和depth推送信息")

    def stop_subscribe_thread(self) -> None:
        """停止订阅流控线程"""
        self._active_subscribe_thread = False
        if self._subscribe_thread:
            self._subscribe_thread.join()
        self._subscribe_thread = None
        self.gateway.write_log("OKX订阅流控线程已停止")

    def _subscribe_process(self) -> None:
        """订阅流控线程函数"""
        while self._active_subscribe_thread:
            try:
                # 从队列中收集所有待处理的订阅和取消订阅请求
                subscribe_requests = []
                unsubscribe_requests = []
                
                # 收集订阅请求
                while True:
                    try:
                        req = self._subscribe_queue.get_nowait()
                        subscribe_requests.append(req)
                    except Empty:
                        break
                
                # 收集取消订阅请求
                while True:
                    try:
                        req = self._unsubscribe_queue.get_nowait()
                        unsubscribe_requests.append(req)
                    except Empty:
                        break
                
                # 处理冲突：如果同一个channel既要订阅又要取消订阅，优先订阅
                # 创建字符串标识符集合用于比较
                subscribe_keys = set()
                for req in subscribe_requests:
                    subscribe_keys.add(f"{req['channel']}:{req['instId']}")
                
                # 过滤掉冲突的取消订阅请求
                final_unsubscribe = []
                for req in unsubscribe_requests:
                    key = f"{req['channel']}:{req['instId']}"
                    if key not in subscribe_keys:
                        final_unsubscribe.append(req)
                
                # 发送取消订阅请求
                if final_unsubscribe:
                    self._send_unsubscribe_batch(final_unsubscribe)
                    time.sleep(self.subscribe_interval)
                
                # 发送订阅请求
                if subscribe_requests:
                    self._send_subscribe_batch(subscribe_requests)
                    time.sleep(self.subscribe_interval)
                
                # 如果没有请求，等待一段时间
                if not subscribe_requests and not final_unsubscribe:
                    time.sleep(0.1)
                    
            except Exception as e:
                self.gateway.write_log(f"订阅流控线程异常：{type(e)} - {str(e)}\n{traceback.format_exc()}")
                time.sleep(1)

    def _send_subscribe_batch(self, requests: list) -> None:
        """发送批量订阅请求，考虑64KB限制"""
        if not requests:
            return
            
        current_batch = []
        current_size = 0
        base_packet_size = len('{"op":"subscribe","args":[]}')  # 基础包大小
        
        for req in requests:
            # 估算单个请求的JSON大小
            req_size = len(json.dumps(req)) + 1  # +1 for comma
            
            # 检查是否超过大小限制
            if current_size + req_size + base_packet_size > self.max_packet_size:
                if current_batch:
                    # 发送当前批次
                    self._send_subscribe_packet(current_batch)
                    time.sleep(self.subscribe_interval)
                    current_batch = [req]
                    current_size = req_size
                else:
                    # 单个请求就超过限制，记录警告并跳过
                    self.gateway.write_log(f"单个订阅请求超过大小限制：{req}")
                    continue
            else:
                current_batch.append(req)
                current_size += req_size
        
        # 发送最后一批
        if current_batch:
            self._send_subscribe_packet(current_batch)

    def _send_unsubscribe_batch(self, requests: list) -> None:
        """发送批量取消订阅请求，考虑64KB限制"""
        if not requests:
            return
            
        current_batch = []
        current_size = 0
        base_packet_size = len('{"op":"unsubscribe","args":[]}')
        
        for req in requests:
            req_size = len(json.dumps(req)) + 1
            
            if current_size + req_size + base_packet_size > self.max_packet_size:
                if current_batch:
                    self._send_unsubscribe_packet(current_batch)
                    time.sleep(self.subscribe_interval)
                    current_batch = [req]
                    current_size = req_size
                else:
                    self.gateway.write_log(f"单个取消订阅请求超过大小限制：{req}")
                    continue
            else:
                current_batch.append(req)
                current_size += req_size
        
        if current_batch:
            self._send_unsubscribe_packet(current_batch)

    def _send_subscribe_packet(self, args: list) -> None:
        """发送订阅数据包"""
        packet = {
            "op": "subscribe",
            "args": args
        }
        self.send_packet(packet)
        self.gateway.write_log(f"发送订阅请求：{len(args)}个频道")

    def _send_unsubscribe_packet(self, args: list) -> None:
        """发送取消订阅数据包"""
        packet = {
            "op": "unsubscribe", 
            "args": args
        }
        self.send_packet(packet)
        self.gateway.write_log(f"发送取消订阅请求：{len(args)}个频道")

    def stop(self) -> None:
        """停止WebSocket客户端"""
        self.stop_subscribe_thread()
        super().stop()

    def subscribe(self, req: SubscribeRequest) -> None:
        """
        Subscribe to market data.

        This function adds subscription requests to the flow control queue
        for the specified trading instrument.

        Parameters:
            req: Subscription request object containing symbol information
        """
        # Get contract by VeighNa symbol
        contract: ContractData | None = self.gateway.get_contract_by_symbol(req.symbol)
        if not contract:
            self.gateway.write_log(f"Failed to subscribe data, symbol not found: {req.symbol}")
            return

        # Add subscribe record
        self.subscribed[req.vt_symbol] = req

        # Create tick object
        tick: TickData = TickData(
            symbol=req.symbol,
            exchange=req.exchange,
            name=contract.name,
            datetime=datetime.now(CHINA_TZ),
            gateway_name=self.gateway_name,
        )
        self.ticks[contract.name] = tick

        # Add subscription requests to queue
        for channel in ["tickers", "books5"]:
            channel_args = {
                "channel": channel,
                "instId": contract.name
            }
            self._subscribe_queue.put(channel_args)

        self.gateway.write_log(f"Added subscription request to queue for {req.symbol}")

    def unsubscribe(self, req: SubscribeRequest) -> None:
        """
        Unsubscribe from market data.

        This function adds unsubscription requests to the flow control queue
        for the specified trading instrument.

        Parameters:
            req: Subscription request object containing symbol information
        """
        # Get contract by VeighNa symbol
        contract: ContractData | None = self.gateway.get_contract_by_symbol(req.symbol)
        if not contract:
            self.gateway.write_log(f"Failed to unsubscribe data, symbol not found: {req.symbol}")
            return

        # Remove subscribe record
        if req.vt_symbol in self.subscribed:
            del self.subscribed[req.vt_symbol]

        # Remove tick object
        if contract.name in self.ticks:
            del self.ticks[contract.name]

        # Add unsubscription requests to queue
        for channel in ["tickers", "books5"]:
            channel_args = {
                "channel": channel,
                "instId": contract.name
            }
            self._unsubscribe_queue.put(channel_args)

        self.gateway.write_log(f"Added unsubscription request to queue for {req.symbol}")


    def on_connected(self) -> None:
        """
        Callback when server is connected.

        This function is called when the websocket connection to the server
        is successfully established. It logs the connection status and
        resubscribes to previously subscribed market data channels.
        """
        self.gateway.write_log("Public API connected")

        for req in list(self.subscribed.values()):
            self.subscribe(req)

    def on_disconnected(self, status_code: int, msg: str) -> None:
        """
        Callback when server is disconnected.

        This function is called when the websocket connection is closed.
        It logs the disconnection status.
        """
        self.gateway.write_log(f"Public API disconnected, code: {status_code}, msg: {msg}")

    def on_packet(self, packet: dict) -> None:
        """
        Callback of data update.

        This function processes different types of market data updates,
        including ticker and depth data. It routes the data to the
        appropriate callback function based on the channel.

        Parameters:
            packet: JSON data received from websocket
        """
        if "event" in packet:
            event: str = packet["event"]
            if event == "subscribe":
                return
            elif event == "unsubscribe":
                return
            elif event == "error":
                code: str = packet["code"]
                msg: str = packet["msg"]
                self.gateway.write_log(f"Public API request failed, status code: {code}, message: {msg}")
        else:
            channel: str = packet["arg"]["channel"]
            callback: Callable | None = self.callbacks.get(channel, None)

            if callback:
                data: list = packet["data"]
                callback(data)

    def on_ticker(self, data: list) -> None:
        """
        Callback of ticker update.

        This function processes the ticker data updates and
        updates the corresponding TickData objects.

        Parameters:
            data: Ticker data from websocket
        """
        for d in data:
            tick: TickData = self.ticks.get(d["instId"])
            if not tick:
                self.gateway.write_log(f"收到未订阅的ticker: {d['instId']}")
                continue

            tick.last_price = float(d["last"])
            tick.open_price = float(d["open24h"])
            tick.high_price = float(d["high24h"])
            tick.low_price = float(d["low24h"])

            # Use OKX volCcy24h for volume (coins like Binance)
            tick.volume = float(d.get("volCcy24h", "0"))

            tick.datetime = parse_timestamp(d["ts"])
            
            # 日志模式：记录ticker推送信息
            if self.log_mode:
                inst_id = d["instId"]
                current_time = datetime.now(CHINA_TZ)
                
                # 初始化计数器和开始时间
                if inst_id not in self.start_time:
                    self.start_time[inst_id] = current_time
                    self.ticker_count[inst_id] = 0
                    self.depth_count[inst_id] = 0
                
                # 更新ticker计数
                self.ticker_count[inst_id] += 1
                
                # 计算频率
                elapsed_seconds = (current_time - self.start_time[inst_id]).total_seconds()
                avg_interval = elapsed_seconds / self.ticker_count[inst_id] if self.ticker_count[inst_id] > 0 else 0
                ticker_frequency = 1 / avg_interval if avg_interval > 0 else 0
                
                # 记录ticker信息
                last_time = self.last_ticker_time.get(inst_id)
                self.last_ticker_time[inst_id] = current_time
                
                log_msg = f"Ticker推送 - {inst_id} - 交易所时间: {tick.datetime.strftime('%Y-%m-%d %H:%M:%S.%f')}, 最新价: {tick.last_price}, 24h成交量: {tick.volume}, 频率: {ticker_frequency:.2f}次/秒, 平均间隔: {avg_interval:.3f}秒"

                if last_time:
                    interval = (current_time - last_time).total_seconds()
                    self.gateway.write_log(f"{log_msg}, 间隔{interval:.3f}秒")
                else:
                    self.gateway.write_log(log_msg)
            
            self.gateway.on_tick(copy(tick))

    def on_depth(self, data: list) -> None:
        """
        Callback of depth update.

        This function processes the order book depth data updates
        and updates the corresponding TickData objects.

        Parameters:
            data: Depth data from websocket
        """
        for d in data:
            tick: TickData = self.ticks.get(d["instId"])
            if not tick:
                self.gateway.write_log(f"收到未订阅的depth: {d['instId']}")
                continue
                
            bids: list = d["bids"]
            asks: list = d["asks"]

            for n in range(min(5, len(bids))):
                price, volume, _, _ = bids[n]
                tick.__setattr__("bid_price_%s" % (n + 1), float(price))
                tick.__setattr__("bid_volume_%s" % (n + 1), float(volume))

            for n in range(min(5, len(asks))):
                price, volume, _, _ = asks[n]
                tick.__setattr__("ask_price_%s" % (n + 1), float(price))
                tick.__setattr__("ask_volume_%s" % (n + 1), float(volume))

            tick.datetime = parse_timestamp(d["ts"])
            
            # 日志模式：记录depth推送信息
            if self.log_mode:
                inst_id = d["instId"]
                current_time = datetime.now(CHINA_TZ)
                
                # 初始化计数器和开始时间
                if inst_id not in self.start_time:
                    self.start_time[inst_id] = current_time
                    self.ticker_count[inst_id] = 0
                    self.depth_count[inst_id] = 0
                
                # 更新depth计数
                self.depth_count[inst_id] += 1
                
                # 计算频率
                elapsed_seconds = (current_time - self.start_time[inst_id]).total_seconds()
                depth_frequency = self.depth_count[inst_id] / elapsed_seconds if elapsed_seconds > 0 else 0
                
                # 记录depth信息
                last_time = self.last_depth_time.get(inst_id)
                self.last_depth_time[inst_id] = current_time
                
                # 获取最优买卖价
                best_bid = float(bids[0][0]) if bids else 0
                best_ask = float(asks[0][0]) if asks else 0
                spread = best_ask - best_bid if best_bid and best_ask else 0
                
                log_msg = f"Depth推送 - {inst_id} - 交易所时间: {tick.datetime.strftime('%Y-%m-%d %H:%M:%S.%f')}, 最优买价: {best_bid}, 最优卖价: {best_ask}, 价差: {spread:.8f}, 频率: {depth_frequency:.2f}次/秒"
                
                if last_time:
                    interval = (current_time - last_time).total_seconds()
                    self.gateway.write_log(f"{log_msg}, 间隔{interval:.3f}秒")
                else:
                    self.gateway.write_log(log_msg)
            
            self.gateway.on_tick(copy(tick))


class PrivateApi(WebsocketClient):
    """The private websocket API of OkxGateway"""

    def __init__(self, gateway: OkxGateway) -> None:
        """
        The init method of the api.

        Parameters:
            gateway: the parent gateway object for pushing callback data.
        """
        super().__init__()

        self.gateway: OkxGateway = gateway
        self.gateway_name: str = gateway.gateway_name
        self.local_orderids: set[str] = gateway.local_orderids

        self.key: str = ""
        self.secret: bytes = b""
        self.passphrase: str = ""

        self.reqid: int = 0
        self.order_count: int = 0
        self.connect_time: int = 0

        self.callbacks: dict[str, Callable] = {
            "login": self.on_login,
            "orders": self.on_order,
            "account": self.on_account,
            "positions": self.on_position,
            "order": self.on_send_order,
            "cancel-order": self.on_cancel_order,
            "batch-cancel-orders": self.on_batch_cancel_orders,
            "error": self.on_api_error
        }

        self.reqid_order_map: dict[str, OrderData] = {}

    def connect(
        self,
        key: str,
        secret: str,
        passphrase: str,
        server: str,
        proxy_host: str,
        proxy_port: int,
    ) -> None:
        """
        Start server connection.

        This method establishes a websocket connection to OKX private data stream.

        Parameters:
            key: API Key for authentication
            secret: API Secret for request signing
            passphrase: API Passphrase for authentication
            server: Server type ("REAL", "AWS", or "DEMO")
            proxy_host: Proxy server hostname or IP
            proxy_port: Proxy server port
        """
        self.key = key
        self.secret = secret.encode()
        self.passphrase = passphrase

        self.connect_time = int(datetime.now().strftime("%y%m%d%H%M%S"))

        server_hosts: dict[str, str] = {
            "REAL": REAL_PRIVATE_HOST,
            "AWS": AWS_PRIVATE_HOST,
            "DEMO": DEMO_PRIVATE_HOST,
        }

        host: str = server_hosts[server]
        self.init(host, proxy_host, proxy_port, 20)

        self.start()

    def on_connected(self) -> None:
        """
        Callback when server is connected.

        This function is called when the websocket connection to the server
        is successfully established. It logs the connection status and
        initiates the login process.
        """
        self.gateway.write_log("Private websocket API connected")
        self.login()

    def on_disconnected(self, status_code: int, msg: str) -> None:
        """
        Callback when server is disconnected.

        This function is called when the websocket connection is closed.
        It logs the disconnection status.
        """
        self.gateway.write_log(f"Private API disconnected, code: {status_code}, msg: {msg}")

    def on_packet(self, packet: dict) -> None:
        """
        Callback of data update.

        This function processes different types of private data updates,
        including orders, account balance, and positions. It routes the data
        to the appropriate callback function.

        Parameters:
            packet: JSON data received from websocket
        """
        if "event" in packet:
            cb_name: str = packet["event"]
        elif "op" in packet:
            cb_name = packet["op"]
        else:
            cb_name = packet["arg"]["channel"]

        callback: Callable | None = self.callbacks.get(cb_name, None)
        if callback:
            callback(packet)

    def on_api_error(self, packet: dict) -> None:
        """
        Callback of API error.

        This function processes error responses from the websocket API.
        It logs the error details for troubleshooting.

        Parameters:
            packet: Error data from websocket
        """
        # Extract error code and message from the response
        code: str = packet["code"]
        msg: str = packet["msg"]

        # 记录API错误信息到数据库
        self._log_error_to_db(
            error_code=int(code) if code.isdigit() else 5,
            error_msg=f"Private API错误: {msg}",
            symbol="PRIVATE_API_ERROR"
        )

        # Log the error with details for debugging
        self.gateway.write_log(f"Private API request failed, status code: {code}, message: {msg}")

    def on_login(self, packet: dict) -> None:
        """
        Callback of user login.

        This function processes the login response and subscribes to
        private data channels if login is successful.

        Parameters:
            packet: Login response data from websocket
        """
        if packet["code"] == '0':
            self.gateway.write_log("Private API login successful")
            self.subscribe_topic()
        else:
            self.gateway.write_log("Private API login failed")

    def on_order(self, packet: dict) -> None:
        """
        Callback of order update.

        This function processes order updates and trade executions.
        It creates OrderData and TradeData objects and pushes them to the gateway.

        Parameters:
            packet: Order update data from websocket
        """
        # Extract order data from packet
        data: list = packet["data"]
        for d in data:
            # Create order object from data
            order: OrderData = self.gateway.parse_order_data(d, self.gateway_name)
            
            with self.gateway.order_lock:
                # Get last order for comparison
                last_order: OrderData = self.gateway.get_order(order.orderid)
                
                # Always push first order update
                if not last_order:
                    self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                    self.gateway.on_order(order)
                    return

                if not last_order.is_active():
                    # self.gateway.write_log(f"忽略订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                    return
                
                # Calculate traded change with precision handling
                status_change: bool = order.status != last_order.status

                # Convert to contracts for precise calculation, then back to coins
                contract: ContractData = self.gateway.get_contract_by_symbol(order.symbol)
                if contract:
                    # Convert to contracts and calculate change
                    order_traded_contracts = order.traded / contract.size
                    last_traded_contracts = last_order.traded / contract.size
                    traded_change_contracts = round_to(order_traded_contracts - last_traded_contracts, contract.min_volume)
                    # Convert back to coins
                    traded_change = float(format_float(traded_change_contracts * contract.size))

                    # Log if precision handling made a difference
                    direct_change = order.traded - last_order.traded
                    if traded_change != direct_change:
                        self.gateway.write_log(f"warning {order.symbol} traded_change precision handled: {direct_change} -> {traded_change}")
                else:
                    # Fallback without precision handling
                    traded_change = order.traded - last_order.traded
                
                # Skip if no meaningful change
                if traded_change < 0:
                    return
                if traded_change == 0 and not status_change:
                    return
                
                # Push order update
                self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                self.gateway.on_order(order)

            # Push trade update on new trade
            if traded_change > 0:
                # Create trade object and push to gateway
                trade: TradeData = TradeData(
                    symbol=order.symbol,
                    exchange=order.exchange,
                    orderid=order.orderid,
                    tradeid=d["tradeId"],
                    direction=order.direction,
                    offset=order.offset,
                    price=float(d["fillPx"]),
                    volume=traded_change,
                    datetime=parse_timestamp(d["uTime"]),
                    gateway_name=self.gateway_name,
                )
                self.gateway.write_log(f"收到成交回报，订单号：{trade.orderid}，成交号：{trade.tradeid}，价格：{trade.price}，数量：{trade.volume}")
                self.gateway.on_trade(trade)

    def on_account(self, packet: dict) -> None:
        """
        Callback of account balance update.

        This function processes account balance updates and creates
        AccountData objects for each asset.

        Parameters:
            packet: Account update data from websocket
        """
        if len(packet["data"]) == 0:
            return

        buf: dict = packet["data"][0]
        for detail in buf["details"]:
            account: AccountData = AccountData(
                accountid=detail["ccy"],
                balance=float(detail["eq"]),
                gateway_name=self.gateway_name,
            )
            account.available = float(detail["availEq"]) if len(detail["availEq"]) != 0 else 0.0
            account.frozen = account.balance - account.available
            self.gateway.on_account(account)

    def on_position(self, packet: dict) -> None:
        """
        Callback of position update.

        This function processes position updates and creates
        PositionData objects for each position.

        Parameters:
            packet: Position update data from websocket
        """
        data: list = packet["data"]
        for d in data:
            name: str = d["instId"]
            contract: ContractData = self.gateway.get_contract_by_name(name)

            # Convert OKX position volume from contracts to coins for Binance compatibility
            pos_contracts: float = float(d.get("pos", "0"))
            pos_coins: float = float(format_float(pos_contracts * contract.size))
            price: float = get_float_value(d, "avgPx")
            pnl: float = get_float_value(d, "upl")

            position: PositionData = PositionData(
                symbol=contract.symbol,
                exchange=Exchange.GLOBAL,
                direction=Direction.NET,
                volume=pos_coins,
                price=price,
                pnl=pnl,
                gateway_name=self.gateway_name,
            )
            self.gateway.on_position(position)

    def on_send_order(self, packet: dict) -> None:
        """
        Callback of send_order.

        This function processes the response to an order placement request.
        It handles errors and rejection cases.

        Parameters:
            packet: Order response data from websocket
        """
        data: list = packet["data"]

        # Wrong parameters
        if packet["code"] != "0":
            if not data:
                order: OrderData = self.reqid_order_map[packet["id"]]
                order.status = Status.REJECTED
                # 构造JSON格式的rejected_reason，包含code和msg字段
                error_json = {
                    "code": packet["code"],
                    "msg": packet["msg"]
                }
                order.rejected_reason = json.dumps(error_json)
                with self.gateway.order_lock:
                    self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                    self.gateway.on_order(order)
                return

        # Process order responses
        for d in data:
            code: str = d["sCode"]
            if code == "0":
                continue
            

            orderid: str = d.get("clOrdId", "")
            if not orderid:
                orderid: str = d.get("ordId", "")
                
            with self.gateway.order_lock:
                order = self.gateway.get_order(orderid)
                if not order:
                    continue
                    
                msg: str = d["sMsg"]
                order.status = Status.REJECTED
                # 构造JSON格式的rejected_reason，包含code和msg字段
                error_json = {
                    "code": code,
                    "msg": msg
                }
                order.rejected_reason = json.dumps(error_json)
                self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
                self.gateway.on_order(copy(order))

            self.gateway.write_log(f"Send order failed, status code: {code}, message: {msg}")

    def on_cancel_order(self, packet: dict) -> None:
        """
        Callback of cancel_order.

        This function processes the response to an order cancellation request.
        It handles errors and logs appropriate messages.

        Parameters:
            packet: Cancel response data from websocket
        """
        # Wrong parameters
        if packet["code"] != "0":
            code: str = packet["code"]
            msg: str = packet["msg"]
            self.gateway.write_log(f"Cancel order failed, status code: {code}, message: {msg}")
            return

        # Failed to process
        data: list = packet["data"]
        for d in data:
            code = d["sCode"]
            if code == "0":
                return

            msg = d["sMsg"]
            self.gateway.write_log(f"Cancel order failed, status code: {code}, message: {msg}")

    def login(self) -> None:
        """
        User login.

        This function prepares and sends a login request to authenticate
        with the websocket API using API credentials.
        """
        now: float = time.time()
        now = now - self.gateway.rest_api.time_offset_ms/1000
        timestamp: str = str(now)
        msg: str = timestamp + "GET" + "/users/self/verify"
        signature: bytes = generate_signature(msg, self.secret)

        packet: dict = {
            "op": "login",
            "args":
            [
                {
                    "apiKey": self.key,
                    "passphrase": self.passphrase,
                    "timestamp": timestamp,
                    "sign": signature.decode("utf-8")
                }
            ]
        }
        self.send_packet(packet)

    def subscribe_topic(self) -> None:
        """
        Subscribe to private data channels.

        This function sends subscription requests for order, account, and
        position updates after successful login.
        """
        packet: dict = {
            "op": "subscribe",
            "args": [
                {
                    "channel": "orders",
                    "instType": "ANY"
                },
                {
                    "channel": "account"
                },
                {
                    "channel": "positions",
                    "instType": "ANY"
                },
            ]
        }
        self.send_packet(packet)

    def send_order(self, req: OrderRequest) -> str:
        """
        Send new order to OKX.

        This function creates and sends a new order request to the exchange.
        It handles different order types and trading modes.

        Parameters:
            req: Order request object containing order details

        Returns:
            str: The VeighNa order ID if successful, empty string otherwise
        """
        # Validate order type is supported by OKX
        if req.type not in ORDERTYPE_VT2OKX:
            self.gateway.write_log(f"Send order failed, order type not supported: {req.type.value}")
            return ""

        # Validate symbol exists in contract map
        contract: ContractData | None = self.gateway.get_contract_by_symbol(req.symbol)
        if not contract:
            self.gateway.write_log(f"Send order failed, symbol not found: {req.symbol}")
            return ""

        # Generate unique local order ID
        self.order_count += 1
        count_str = str(self.order_count).rjust(6, "0")
        orderid = f"{self.connect_time}{count_str}"

        # Convert volume from coins to contracts for OKX API
        # req.volume is in coins (like Binance), but OKX sz needs contracts
        volume_contracts = round_to(req.volume / contract.size, contract.min_volume)

        # Prepare order parameters for OKX API
        args: dict = {
            "instId": contract.name,
            "clOrdId": orderid,
            "side": DIRECTION_VT2OKX[req.direction],
            "ordType": ORDERTYPE_VT2OKX[req.type],
            "px": str(req.price),
            "sz": str(volume_contracts)
        }

        # Set trading mode based on product type
        # "cash" for spot trading, "cross" for futures/swap with cross margin
        if contract.product == Product.SPOT:
            args["tdMode"] = "cash"
        else:
            args["tdMode"] = "cross"

        # Create websocket request with unique request ID
        self.reqid += 1
        packet: dict = {
            "id": str(self.reqid),
            "op": "order",
            "args": [args]
        }
        self.send_packet(packet)

        # Create order data object and push to gateway
        order: OrderData = req.create_order_data(orderid, self.gateway_name)
        with self.gateway.order_lock:
            self.gateway.write_log(f"收到订单回报，订单号：{order.orderid}，状态：{order.status}，已成交：{order.traded}，剩余：{order.volume - order.traded}")
            self.gateway.on_order(order)

        # Return VeighNa order ID (gateway_name.orderid)
        return str(order.vt_orderid)

    def cancel_order(self, req: CancelRequest) -> None:
        """
        Cancel existing order on OKX.

        This function sends a request to cancel an existing order on the exchange.
        It determines whether to use client order ID or exchange order ID.

        Parameters:
            req: Cancel request object containing order details
        """
        # Validate symbol exists in contract map
        contract: ContractData | None = self.gateway.get_contract_by_symbol(req.symbol)
        if not contract:
            self.gateway.write_log(f"Cancel order failed, symbol not found: {req.symbol}")
            return

        # Initialize cancel parameters with instrument ID
        args: dict = {"instId": contract.name}

        # Determine the type of order ID to use for cancellation
        # OKX supports both client order ID and exchange order ID for cancellation
        if req.orderid in self.local_orderids:
            # Use client order ID if it was created by this gateway instance
            args["clOrdId"] = req.orderid
        else:
            # Use exchange order ID if it came from another source
            args["ordId"] = req.orderid

        # Create websocket request with unique request ID
        self.reqid += 1
        packet: dict = {
            "id": str(self.reqid),
            "op": "cancel-order",
            "args": [args]
        }

        # Send the cancellation request
        self.send_packet(packet)

    def cancel_all_orders(self) -> None:
        """
        Cancel all open orders using OKX batch-cancel-orders API.

        This function first queries all open orders via REST API, then sends batch cancellation
        requests in groups of up to 20 orders (OKX API limit).
        """
        # Query open orders first via REST API to get order list
        self.gateway.rest_api.query_orders(for_cancel_all=True)

    def batch_cancel_orders(self, order_list: list[dict]) -> None:
        """
        Cancel multiple orders using batch-cancel-orders API.

        This function sends batch cancellation requests for multiple orders.
        Maximum 20 orders per request as per OKX API limits.

        Parameters:
            order_list: List of order dicts containing instId and ordId/clOrdId
        """
        if not order_list:
            self.gateway.write_log("No orders to cancel")
            return

        # Split orders into batches of maximum 20 orders (OKX API limit)
        batch_size = 20
        for i in range(0, len(order_list), batch_size):
            batch = order_list[i:i + batch_size]
            
            # Create websocket request with unique request ID
            self.reqid += 1
            packet: dict = {
                "id": str(self.reqid),
                "op": "batch-cancel-orders",
                "args": batch
            }
            
            # Send the batch cancellation request
            self.send_packet(packet)
            self.gateway.write_log(f"Sent batch cancel request for {len(batch)} orders")

    def on_batch_cancel_orders(self, packet: dict) -> None:
        """
        Callback of batch-cancel-orders.

        This function processes the response to a batch order cancellation request.
        It handles successful, partial success, and failed cancellations according to OKX API specification.
        
        Response codes:
        - "0": All orders cancelled successfully
        - "1": All orders failed to cancel
        - "2": Partial success (some orders cancelled, some failed)

        Parameters:
            packet: Batch cancel response data from websocket
        """
        # Check overall response code
        code: str = packet.get("code", "")
        msg: str = packet.get("msg", "")
        
        if code == "0":
            # All orders cancelled successfully
            data: list = packet.get("data", [])
            self.gateway.write_log(f"Batch cancel completed: All {len(data)} orders cancelled successfully")
        elif code == "1":
            # All orders failed to cancel
            self.gateway.write_log(f"Batch cancel failed: All orders failed to cancel, message: {msg}")
        elif code == "2":
            # Partial success - some orders cancelled, some failed
            self.gateway.write_log(f"Batch cancel partial success: Some orders cancelled, some failed")
        else:
            # Unknown response code
            self.gateway.write_log(f"Batch cancel orders failed, unknown status code: {code}, message: {msg}")
            return

        # Process individual order results for all cases (success, partial, or failure)
        data: list = packet.get("data", [])
        success_count = 0
        fail_count = 0
        
        for d in data:
            order_code: str = d.get("sCode", "")
            if order_code == "0":
                success_count += 1
            else:
                fail_count += 1
                order_msg: str = d.get("sMsg", "")
                order_id: str = d.get("ordId", "") or d.get("clOrdId", "")
                self.gateway.write_log(f"Cancel order {order_id} failed, code: {order_code}, message: {order_msg}")
        
        # Log detailed summary
        total = success_count + fail_count
        if total > 0:
            self.gateway.write_log(f"Batch cancel summary: {success_count}/{total} orders cancelled successfully, {fail_count} failed")


def generate_signature(msg: str, secret_key: bytes) -> bytes:
    """
    Generate signature from message.

    This function creates an HMAC-SHA256 signature required for
    authenticated API requests to OKX.

    Parameters:
        msg: Message to be signed
        secret_key: API secret key in bytes

    Returns:
        bytes: Base64 encoded signature
    """
    return base64.b64encode(hmac.new(secret_key, msg.encode(), hashlib.sha256).digest())


def generate_timestamp() -> str:
    """
    Generate current timestamp.

    This function creates an ISO format timestamp with milliseconds
    required for OKX API requests.

    Returns:
        str: ISO 8601 formatted timestamp with Z suffix
    """
    now: datetime = datetime.utcnow()
    timestamp: str = now.isoformat("T", "milliseconds")
    return timestamp + "Z"


def parse_timestamp(timestamp: str) -> datetime:
    """
    Parse timestamp to datetime.

    This function converts OKX timestamp to a datetime object
    with UTC timezone.

    Parameters:
        timestamp: OKX timestamp in milliseconds

    Returns:
        datetime: Datetime object with UTC timezone
    """
    dt: datetime = datetime.fromtimestamp(int(timestamp) / 1000, tz=CHINA_TZ)
    return dt


def get_float_value(data: dict, key: str) -> float:
    """
    Get decimal number from float value.

    This function safely extracts a float value from a dictionary
    and handles empty or missing values.

    Parameters:
        data: Dictionary containing the value
        key: Key to extract from the dictionary

    Returns:
        float: Extracted value or 0.0 if not found
    """
    data_str: str = data.get(key, "")
    if not data_str:
        return 0.0
    try:
        return float(data_str)
    except ValueError:
        return 0.0

def format_float(f: float) -> str:
    """
    Convert float number to string with correct precision.

    Fix potential error -1111: Parameter 'quantity' has too much precision
    """
    return format_float_positional(f, trim='-')


