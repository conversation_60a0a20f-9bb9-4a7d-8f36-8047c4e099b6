{% extends "base.html" %}

{% block title %}Position Monitor{% endblock %}

{% block content %}
<div class="row align-items-center mb-4">
    <div class="col-md-10">
        <h2>Position Monitor</h2>
    </div>
    <div class="col-md-2">
        <a href="{{ url_for('reset_leverage') }}" class="btn btn-primary btn-block">Leverage</a>
    </div>
</div>

<!-- 筛选表单 -->
<div class="mb-3">
    
    <form method="GET" action="{{ url_for('position_monitor') }}" class="row">
        <div class="col-md-5">
            <div class="form-group">
                <label for="username">Username</label>
                <select class="form-control" id="username" name="username">
                    <option value="">All Users</option>
                    {% for username in usernames %}
                    <option value="{{ username }}" {% if username == selected_username %}selected{% endif %}>{{ username }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <div class="col-md-5">
            <div class="form-group">
                <label for="symbol">Symbol</label>
                <input type="text" class="form-control" id="symbol" name="symbol" list="symbol-list" placeholder="Search or select symbol" value="{{ selected_symbol }}">
                <datalist id="symbol-list">
                    {% for symbol in symbols %}
                    <option value="{{ symbol }}">
                    {% endfor %}
                </datalist>
            </div>
        </div>
        
        <div class="col-md-2">
            <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn btn-primary form-control">Filter</button>
            </div>
        </div>
    </form>
</div>

<!-- 数据表格 -->
<div class="table-responsive">
    <table class="table table-striped table-hover" id="positionTable">
        <thead class="thead-dark">
            <tr>
                <th class="sortable" data-column="username" style="cursor: pointer;">
                    Username
                    <span class="sort-indicator" data-column="username">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="symbol" style="cursor: pointer;">
                    Symbol
                    <span class="sort-indicator" data-column="symbol">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="exchange" style="cursor: pointer;">
                    Exchange
                    <span class="sort-indicator" data-column="exchange">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="leverage" style="cursor: pointer;">
                    Leverage
                    <span class="sort-indicator" data-column="leverage">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="position" style="cursor: pointer;">
                    Position
                    <span class="sort-indicator" data-column="position">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="current_price" style="cursor: pointer;">
                    Current Price
                    <span class="sort-indicator" data-column="current_price">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="position_value" style="cursor: pointer;">
                    Position Value
                    <span class="sort-indicator" data-column="position_value">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="max_notional_value" style="cursor: pointer;">
                    Max Notional Value
                    <span class="sort-indicator" data-column="max_notional_value">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="update_time" style="cursor: pointer;">
                    Update Time
                    <span class="sort-indicator" data-column="update_time">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            {% for position in positions %}
            <tr>
                <td data-value="{{ position.username }}">{{ position.username }}</td>
                <td data-value="{{ position.symbol }}">{{ position.symbol }}</td>
                <td data-value="{{ position.exchange }}">{{ position.exchange }}</td>
                <td data-value="{{ position.leverage }}">{{ position.leverage }}</td>
                <td data-value="{{ position.position }}">{{ position.position }}</td>
                <td data-value="{{ position.current_price }}">{{ position.current_price }}</td>
                <td data-value="{{ position.position_value if position.position_value else 0 }}">{{ position.formatted_position_value }}</td>
                <td data-value="{{ position.max_notional_value }}">{{ position.max_notional_value }}</td>
                <td data-value="{{ position.update_time.timestamp() }}">{{ position.update_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>
                    <a href="{{ url_for('reset_leverage', username=position.username, symbol=position.symbol) }}" class="btn btn-sm btn-primary">Leverage</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% if not positions %}
<div class="alert alert-info">
    <h4>No Position Data Found</h4>
    <p>There are no positions to display{% if selected_username or selected_symbol %} for your current filters{% endif %}.</p>
</div>
{% endif %}

<style>
.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
.sort-indicator {
    margin-left: 5px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let sortState = {
        column: null,
        direction: 'desc'  // 默认为降序（从大到小）
    };

    // 为所有可排序的表头添加点击事件
    document.querySelectorAll('.sortable').forEach(function(header) {
        header.addEventListener('click', function() {
            const column = this.getAttribute('data-column');

            // 如果点击的是当前排序列，则切换排序方向
            if (sortState.column === column) {
                sortState.direction = sortState.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // 如果点击的是新列，设置为降序（从大到小）
                sortState.column = column;
                sortState.direction = 'desc';
            }

            sortTable(column, sortState.direction);
            updateSortIndicators(column, sortState.direction);
        });
    });

    function sortTable(column, direction) {
        const table = document.getElementById('positionTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        rows.sort(function(a, b) {
            let aValue, bValue;

            if (column === 'username') {
                aValue = a.cells[0].getAttribute('data-value');
                bValue = b.cells[0].getAttribute('data-value');
                return direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            } else if (column === 'symbol') {
                aValue = a.cells[1].getAttribute('data-value');
                bValue = b.cells[1].getAttribute('data-value');
                return direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            } else if (column === 'exchange') {
                aValue = a.cells[2].getAttribute('data-value');
                bValue = b.cells[2].getAttribute('data-value');
                return direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            } else if (column === 'leverage') {
                aValue = parseInt(a.cells[3].getAttribute('data-value')) || 0;
                bValue = parseInt(b.cells[3].getAttribute('data-value')) || 0;
                return direction === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            } else if (column === 'position') {
                aValue = parseFloat(a.cells[4].getAttribute('data-value')) || 0;
                bValue = parseFloat(b.cells[4].getAttribute('data-value')) || 0;
                return direction === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            } else if (column === 'current_price') {
                aValue = parseFloat(a.cells[5].getAttribute('data-value')) || 0;
                bValue = parseFloat(b.cells[5].getAttribute('data-value')) || 0;
                return direction === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            } else if (column === 'position_value') {
                aValue = parseFloat(a.cells[6].getAttribute('data-value')) || 0;
                bValue = parseFloat(b.cells[6].getAttribute('data-value')) || 0;
                return direction === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            } else if (column === 'max_notional_value') {
                aValue = parseInt(a.cells[7].getAttribute('data-value')) || 0;
                bValue = parseInt(b.cells[7].getAttribute('data-value')) || 0;
                return direction === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            } else if (column === 'update_time') {
                aValue = parseFloat(a.cells[8].getAttribute('data-value')) || 0;
                bValue = parseFloat(b.cells[8].getAttribute('data-value')) || 0;
                return direction === 'asc'
                    ? aValue - bValue
                    : bValue - aValue;
            }
        });

        // 重新排列表格行
        rows.forEach(function(row) {
            tbody.appendChild(row);
        });
    }

    function updateSortIndicators(activeColumn, direction) {
        // 重置所有排序指示器
        document.querySelectorAll('.sort-indicator i').forEach(function(icon) {
            icon.className = 'fas fa-sort text-muted';
        });

        // 更新当前活跃列的排序指示器
        const activeIndicator = document.querySelector(`[data-column="${activeColumn}"] .sort-indicator i`);
        if (activeIndicator) {
            activeIndicator.className = direction === 'asc'
                ? 'fas fa-sort-up text-primary'
                : 'fas fa-sort-down text-primary';
        }
    }
});
</script>

{% endblock %}